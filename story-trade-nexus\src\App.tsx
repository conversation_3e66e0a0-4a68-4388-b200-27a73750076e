
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/lib/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import AdminRoute from "@/components/auth/AdminRoute";
import Index from "./pages/Index";
import SignIn from "./pages/SignIn";
import Join from "./pages/Join";
import ForgotPassword from "./pages/ForgotPassword";
import VerifyEmail from "./pages/VerifyEmail";
import NotFound from "./pages/NotFound";
import Unauthorized from "./pages/Unauthorized";
import AddBooks from "./pages/AddBooks";
import BrowseBooks from "./pages/BrowseBooks";
import BookDetail from "./pages/BookDetail";
import HowItWorksPage from "./pages/HowItWorks";
import FAQ from "./pages/FAQ";
import ContactUs from "./pages/ContactUs";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import Terms from "./pages/Terms";
import DataDeletion from "./pages/DataDeletion";
import UserAccount from "./pages/UserAccount";
import AdminDashboard from "./pages/AdminDashboard";
import AdminBookApprovals from "./pages/AdminBookApprovals";
import AdminUsers from "./pages/AdminUsers";
import AdminUtilities from "./pages/AdminUtilities";
import AdminSettings from "./pages/AdminSettings";
import AdminContactMessages from "./pages/AdminContactMessages";
import MyBooks from "./pages/MyBooks";
import SeedBooks from "./pages/SeedBooks";
import DatabaseBooks from "./pages/DatabaseBooks";
import AdminSetup from "./pages/AdminSetup";
import AdminDiagnostic from "./pages/AdminDiagnostic";

const queryClient = new QueryClient();

const App = () => (
  <BrowserRouter>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<Index />} />
            <Route path="/signin" element={<SignIn />} />
            <Route path="/join" element={<Join />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/verify-email" element={<VerifyEmail />} />
            <Route path="/browse" element={<BrowseBooks />} />
            <Route path="/books/:id" element={<BookDetail />} />
            <Route path="/how-it-works" element={<HowItWorksPage />} />
            <Route path="/faq" element={<FAQ />} />
            <Route path="/contact" element={<ContactUs />} />
            <Route path="/privacy" element={<PrivacyPolicy />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/data-deletion" element={<DataDeletion />} />

            {/* Protected routes - require authentication and email verification */}
            <Route path="/dashboard" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your dashboard"
                verificationMessage="You need to verify your email address before you can access your dashboard and manage your books."
              >
                <UserAccount />
              </ProtectedRoute>
            } />

            <Route path="/profile" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your profile"
              >
                <UserAccount />
              </ProtectedRoute>
            } />

            <Route path="/my-books" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your books"
              >
                <MyBooks />
              </ProtectedRoute>
            } />

            <Route path="/settings" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your settings"
              >
                <UserAccount />
              </ProtectedRoute>
            } />

            <Route path="/add-books" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="add new books"
                verificationMessage="You need to verify your email address before you can add books to the platform. This helps ensure the quality and security of our book-sharing community."
              >
                <AddBooks />
              </ProtectedRoute>
            } />



            <Route path="/wishlist" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your wishlist"
              >
                <div>Wishlist Page (Coming Soon)</div>
              </ProtectedRoute>
            } />

            <Route path="/messages" element={
              <ProtectedRoute
                requireVerification={true}
                showVerificationUI={true}
                featureName="access your messages"
              >
                <div>Messages Page (Coming Soon)</div>
              </ProtectedRoute>
            } />

            {/* Development routes */}
            <Route path="/seed-books" element={<SeedBooks />} />
            <Route path="/database-books" element={<DatabaseBooks />} />
            <Route path="/admin-setup" element={<AdminSetup />} />
            <Route path="/admin-diagnostic" element={<AdminDiagnostic />} />

            {/* Admin routes */}
            <Route path="/unauthorized" element={<Unauthorized />} />

            <Route path="/admin" element={
              <AdminRoute>
                <AdminDashboard />
              </AdminRoute>
            } />

            <Route path="/admin/books" element={
              <AdminRoute>
                <AdminBookApprovals />
              </AdminRoute>
            } />

            <Route path="/admin/users" element={
              <AdminRoute>
                <AdminUsers />
              </AdminRoute>
            } />

            <Route path="/admin/utilities" element={
              <AdminRoute>
                <AdminUtilities />
              </AdminRoute>
            } />

            <Route path="/admin/settings" element={
              <AdminRoute>
                <AdminSettings />
              </AdminRoute>
            } />

            <Route path="/admin/messages" element={
              <AdminRoute>
                <AdminContactMessages />
              </AdminRoute>
            } />

            {/* Catch-all route for 404 */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  </BrowserRouter>
);

export default App;

