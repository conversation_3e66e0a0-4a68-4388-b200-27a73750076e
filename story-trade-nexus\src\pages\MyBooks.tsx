import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { BookOpen, Edit, Plus, Filter, Search, Clock, CheckCircle, XCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button-variants';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/lib/AuthContext';
import { Book, BookApprovalStatus, BookStatus } from '@/types';
import MainLayout from '@/components/layouts/MainLayout';
import { EditBookModal } from '@/components/EditBookModal';
import { BookStatusBadge } from '@/components/BookStatusBadge';
import { getBooksByOwner } from '@/lib/bookService';
import { toast } from 'sonner';

const MyBooks = () => {
  const { currentUser, userData } = useAuth();
  const [userBooks, setUserBooks] = useState<Book[]>([]);
  const [filteredBooks, setFilteredBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingBook, setEditingBook] = useState<Book | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [approvalFilter, setApprovalFilter] = useState<string>('all');

  useEffect(() => {
    const fetchUserBooks = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);
        const books = await getBooksByOwner(currentUser.uid);
        setUserBooks(books);
        setFilteredBooks(books);
      } catch (error) {
        console.error('Error fetching user books:', error);
        toast.error('Failed to load your books');
      } finally {
        setLoading(false);
      }
    };

    fetchUserBooks();
  }, [currentUser]);

  // Filter books based on search term and filters
  useEffect(() => {
    let filtered = userBooks;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(book =>
        book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
        book.genre.some(g => g.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(book => book.status === statusFilter);
    }

    // Approval filter
    if (approvalFilter !== 'all') {
      filtered = filtered.filter(book => {
        if (approvalFilter === 'pending') return book.approvalStatus === BookApprovalStatus.Pending;
        if (approvalFilter === 'approved') return book.approvalStatus === BookApprovalStatus.Approved || !book.approvalStatus;
        if (approvalFilter === 'rejected') return book.approvalStatus === BookApprovalStatus.Rejected;
        return true;
      });
    }

    setFilteredBooks(filtered);
  }, [userBooks, searchTerm, statusFilter, approvalFilter]);

  // Edit book functions
  const handleEditBook = (book: Book) => {
    setEditingBook(book);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setEditingBook(null);
    setIsEditModalOpen(false);
  };

  const handleBookUpdated = (updatedBook: Book) => {
    setUserBooks(prevBooks =>
      prevBooks.map(book =>
        book.id === updatedBook.id ? updatedBook : book
      )
    );
    toast.success('Book updated successfully');
  };

  const getApprovalStatusBadge = (status?: BookApprovalStatus) => {
    switch (status) {
      case BookApprovalStatus.Pending:
        return (
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </span>
        );
      case BookApprovalStatus.Approved:
        return (
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved
          </span>
        );
      case BookApprovalStatus.Rejected:
        return (
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved
          </span>
        );
    }
  };

  const displayName = userData?.displayName || currentUser?.displayName || currentUser?.email?.split('@')[0] || 'Reader';

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-navy-800 mb-2">
                My Books
              </h1>
              <p className="text-gray-600">Manage your book collection</p>
            </div>
            <div className="mt-4 md:mt-0">
              <Link to="/add-books">
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add New Book
                </Button>
              </Link>
            </div>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search books..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Available">Available</SelectItem>
                <SelectItem value="Sold Out">Sold Out</SelectItem>
                <SelectItem value="Rented Out">Rented Out</SelectItem>
              </SelectContent>
            </Select>

            <Select value={approvalFilter} onValueChange={setApprovalFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by approval" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Approvals</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <div className="text-sm text-gray-600 flex items-center">
              <Filter className="h-4 w-4 mr-2" />
              {filteredBooks.length} of {userBooks.length} books
            </div>
          </div>
        </div>

        {/* Books Grid */}
        <div className="bg-white rounded-lg shadow-md p-6">
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                <div key={i} className="bg-gray-200 rounded-lg h-80 animate-pulse"></div>
              ))}
            </div>
          ) : filteredBooks.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {filteredBooks.map((book) => (
                <div key={book.id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow border">
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={book.imageUrl}
                      alt={book.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 right-2 flex flex-col gap-1">
                      <BookStatusBadge status={book.status} nextAvailableDate={book.nextAvailableDate} />
                      {getApprovalStatusBadge(book.approvalStatus)}
                    </div>
                    <div className="absolute top-2 left-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditBook(book)}
                        className="bg-white/90 hover:bg-white"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1">
                      {book.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-2">by {book.author}</p>
                    
                    <div className="flex items-center justify-between text-sm mb-2">
                      <span className="text-gray-700 flex items-center">
                        <BookOpen className="h-3.5 w-3.5 mr-1" />
                        {book.condition}
                      </span>
                      <span className="text-gray-600">
                        {book.genre.slice(0, 2).join(', ')}
                        {book.genre.length > 2 && '...'}
                      </span>
                    </div>

                    <div className="text-sm text-gray-600">
                      {book.availability}
                      {book.price && (
                        <span className="block text-burgundy-600 font-medium">₹{book.price}</span>
                      )}
                      {book.rentalPrice && (
                        <span className="block text-burgundy-600 font-medium">
                          ₹{book.rentalPrice} {book.rentalPeriod}
                        </span>
                      )}
                    </div>

                    {book.approvalStatus === BookApprovalStatus.Rejected && book.rejectionReason && (
                      <div className="mt-2 p-2 bg-red-50 rounded text-xs text-red-700">
                        <strong>Rejection reason:</strong> {book.rejectionReason}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {userBooks.length === 0 ? 'No books found' : 'No books match your filters'}
              </h3>
              <p className="text-gray-600 mb-4">
                {userBooks.length === 0 
                  ? "You haven't added any books yet. Start building your collection!"
                  : 'Try adjusting your search or filter criteria.'
                }
              </p>
              {userBooks.length === 0 && (
                <Link to="/add-books">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Book
                  </Button>
                </Link>
              )}
            </div>
          )}
        </div>

        {/* Edit Book Modal */}
        {editingBook && (
          <EditBookModal
            book={editingBook}
            isOpen={isEditModalOpen}
            onClose={handleCloseEditModal}
            onBookUpdated={handleBookUpdated}
          />
        )}
      </div>
    </MainLayout>
  );
};

export default MyBooks;
