import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { BookOpen, MapPin, Calendar, BookMarked, Clock, AlertCircle, User, Plus, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/lib/AuthContext';
import { Book, BookApprovalStatus } from '@/types';
import MainLayout from '@/components/layouts/MainLayout';
import BookCard from '@/components/BookCard';
import { EditBookModal } from '@/components/EditBookModal';
import { BookStatusBadge } from '@/components/BookStatusBadge';
import { getBooksByOwner } from '@/lib/bookService';
import { toast } from 'sonner';

const Dashboard = () => {
  const { currentUser, userData } = useAuth();
  const [userBooks, setUserBooks] = useState<Book[]>([]);
  const [pendingBooks, setPendingBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingBook, setEditingBook] = useState<Book | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  useEffect(() => {
    const fetchUserBooks = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);
        const books = await getBooksByOwner(currentUser.uid);
        setUserBooks(books);

        // Filter out pending books
        const pending = books.filter(book => book.approvalStatus === BookApprovalStatus.Pending);
        setPendingBooks(pending);
      } catch (error) {
        console.error('Error fetching user books:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserBooks();
  }, [currentUser]);

  // Get user display name
  const displayName = userData?.displayName || currentUser?.displayName || currentUser?.email?.split('@')[0] || 'Reader';

  // Get user email
  const email = userData?.email || currentUser?.email || 'No email provided';

  // Get book counts
  const booksAdded = userBooks.length || 0;
  const approvedBooks = userBooks.filter(book =>
    book.approvalStatus === BookApprovalStatus.Approved || !book.approvalStatus).length;
  const pendingCount = pendingBooks.length;

  // Edit book functions
  const handleEditBook = (book: Book) => {
    setEditingBook(book);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setEditingBook(null);
    setIsEditModalOpen(false);
  };

  const handleBookUpdated = (updatedBook: Book) => {
    setUserBooks(prevBooks =>
      prevBooks.map(book =>
        book.id === updatedBook.id ? updatedBook : book
      )
    );
    toast.success('Book updated successfully');
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-navy-800 mb-2">
                Welcome, {displayName}!
              </h1>
              <p className="text-gray-600">Manage your books and exchanges</p>
            </div>
            <div className="mt-4 md:mt-0">
              <Link to="/add-books">
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add New Books
                </Button>
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* User Info Card */}
            <div className="bg-gray-50 rounded-lg p-5">
              <h2 className="text-lg font-medium text-navy-800 mb-4">Your Profile</h2>
              <div className="space-y-3">
                <div className="flex items-start">
                  <User className="h-5 w-5 text-burgundy-500 mr-3 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Name</p>
                    <p className="text-gray-600">{displayName}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <BookOpen className="h-5 w-5 text-burgundy-500 mr-3 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-gray-600">{email}</p>
                  </div>
                </div>
                <div className="mt-4">
                  <Link to="/profile">
                    <Button variant="outline" size="sm" className="w-full">
                      View Full Profile
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            {/* Stats Card */}
            <div className="bg-gray-50 rounded-lg p-5 col-span-2">
              <h2 className="text-lg font-medium text-navy-800 mb-4">Your Book Stats</h2>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="bg-white p-4 rounded-md shadow-sm">
                  <div className="text-3xl font-bold text-burgundy-600">{booksAdded}</div>
                  <div className="text-sm text-gray-600">Total Books</div>
                </div>
                <div className="bg-white p-4 rounded-md shadow-sm">
                  <div className="text-3xl font-bold text-green-600">{approvedBooks}</div>
                  <div className="text-sm text-gray-600">Active Listings</div>
                </div>
                <div className="bg-white p-4 rounded-md shadow-sm">
                  <div className="text-3xl font-bold text-amber-600">{pendingCount}</div>
                  <div className="text-sm text-gray-600">Pending Approval</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Your Books Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-navy-800">Your Books</h2>
            <Link to="/my-books">
              <Button variant="link" className="text-burgundy-600">
                View All
              </Button>
            </Link>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm overflow-hidden">
                  <Skeleton className="h-48 w-full" />
                  <div className="p-4">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-8 w-full mt-4" />
                  </div>
                </div>
              ))}
            </div>
          ) : userBooks.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {userBooks.slice(0, 4).map((book) => (
                <div key={book.id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow">
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={book.imageUrl}
                      alt={book.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 right-2">
                      <BookStatusBadge status={book.status} nextAvailableDate={book.nextAvailableDate} />
                    </div>
                    <div className="absolute top-2 left-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditBook(book)}
                        className="bg-white/90 hover:bg-white"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1">
                      {book.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-2">by {book.author}</p>

                    <div className="flex items-center justify-between text-sm mb-2">
                      <span className="text-gray-700 flex items-center">
                        <BookOpen className="h-3.5 w-3.5 mr-1" />
                        {book.condition}
                      </span>
                      {book.approvalStatus === BookApprovalStatus.Pending && (
                        <span className="text-amber-600 flex items-center">
                          <Clock className="h-3.5 w-3.5 mr-1" />
                          Pending
                        </span>
                      )}
                    </div>

                    <div className="text-sm text-gray-600">
                      {book.availability}
                      {book.price && (
                        <span className="block text-burgundy-600 font-medium">₹{book.price}</span>
                      )}
                      {book.rentalPrice && (
                        <span className="block text-burgundy-600 font-medium">
                          ₹{book.rentalPrice} {book.rentalPeriod}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-4">You haven't added any books yet</div>
              <Link to="/add-books">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Book
                </Button>
              </Link>
            </div>
          )}
        </div>

        {/* Edit Book Modal */}
        {editingBook && (
          <EditBookModal
            book={editingBook}
            isOpen={isEditModalOpen}
            onClose={handleCloseEditModal}
            onBookUpdated={handleBookUpdated}
          />
        )}
      </div>
    </MainLayout>
  );
};

export default Dashboard;
